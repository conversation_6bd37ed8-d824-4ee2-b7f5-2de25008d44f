import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/database'
import { z } from 'zod'

// Middleware to verify inter-server requests
function verifyInterServerRequest(request: NextRequest) {
  const apiKey = request.headers.get('X-API-Key')
  const serverSource = request.headers.get('X-Server-Source')
  
  if (!apiKey || apiKey !== process.env.STAFF_API_KEY) {
    return false
  }
  
  if (serverSource !== 'staff') {
    return false
  }
  
  return true
}

const updateStudentSchema = z.object({
  name: z.string().min(1).optional(),
  phone: z.string().min(1).optional(),
  email: z.string().email().optional(),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']).optional(),
  branch: z.string().min(1).optional(),
  emergencyContact: z.string().optional(),
  status: z.enum(['ACTIVE', 'DROPPED', 'PAUSED', 'COMPLETED']).optional(),
  currentGroupReferenceId: z.string().optional(),
})

// GET /api/inter-server/students/[id] - Get student data
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      include: {
        studentProfile: {
          include: {
            currentGroupReference: {
              include: {
                teacherReference: true,
              }
            },
            payments: {
              orderBy: { createdAt: 'desc' },
            },
            attendances: {
              orderBy: { createdAt: 'desc' },
              take: 20,
            },
            assessments: {
              orderBy: { createdAt: 'desc' },
            }
          }
        }
      }
    })

    if (!user || user.role !== 'STUDENT') {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    const studentData = {
      id: user.id,
      name: user.name,
      phone: user.phone,
      email: user.email,
      level: user.studentProfile?.level,
      branch: user.studentProfile?.branch,
      emergencyContact: user.studentProfile?.emergencyContact,
      status: user.studentProfile?.status,
      currentGroup: user.studentProfile?.currentGroupReference,
      payments: user.studentProfile?.payments,
      attendance: user.studentProfile?.attendances,
      assessments: user.studentProfile?.assessments,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }

    return NextResponse.json(studentData)
  } catch (error) {
    console.error('Error fetching student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/inter-server/students/[id] - Update student data
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = updateStudentSchema.parse(body)

    // Check if student exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id },
      include: { studentProfile: true }
    })

    if (!existingUser || existingUser.role !== 'STUDENT') {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Prepare user update data
    const userUpdateData: { name?: string; phone?: string; email?: string } = {}
    if (validatedData.name) userUpdateData.name = validatedData.name
    if (validatedData.phone) userUpdateData.phone = validatedData.phone
    if (validatedData.email) userUpdateData.email = validatedData.email

    // Prepare student profile update data
    const profileUpdateData: {
      level?: string;
      branch?: string;
      emergencyContact?: string;
      status?: string;
      currentGroupReferenceId?: string
    } = {}
    if (validatedData.level) profileUpdateData.level = validatedData.level
    if (validatedData.branch) profileUpdateData.branch = validatedData.branch
    if (validatedData.emergencyContact) profileUpdateData.emergencyContact = validatedData.emergencyContact
    if (validatedData.status) profileUpdateData.status = validatedData.status
    if (validatedData.currentGroupReferenceId) profileUpdateData.currentGroupReferenceId = validatedData.currentGroupReferenceId

    // Update user and student profile
    const updatedUser = await prisma.user.update({
      where: { id: params.id },
      data: {
        ...userUpdateData,
        updatedAt: new Date(),
        studentProfile: existingUser.studentProfile ? {
          update: {
            ...profileUpdateData,
            updatedAt: new Date(),
          }
        } : undefined
      },
      include: {
        studentProfile: {
          include: {
            currentGroupReference: true,
          }
        }
      }
    })

    const studentData = {
      id: updatedUser.id,
      name: updatedUser.name,
      phone: updatedUser.phone,
      email: updatedUser.email,
      level: updatedUser.studentProfile?.level,
      branch: updatedUser.studentProfile?.branch,
      emergencyContact: updatedUser.studentProfile?.emergencyContact,
      status: updatedUser.studentProfile?.status,
      currentGroup: updatedUser.studentProfile?.currentGroupReference,
      updatedAt: updatedUser.updatedAt,
    }

    return NextResponse.json(studentData)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    
    console.error('Error updating student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/inter-server/students/[id]/sync - Sync student data
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      include: {
        studentProfile: {
          include: {
            currentGroupReference: {
              include: {
                teacherReference: true,
              }
            },
            payments: {
              orderBy: { createdAt: 'desc' },
              take: 10,
            },
            attendances: {
              orderBy: { createdAt: 'desc' },
              take: 20,
            },
            assessments: {
              orderBy: { createdAt: 'desc' },
              take: 10,
            }
          }
        }
      }
    })

    if (!user || user.role !== 'STUDENT') {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    const studentData = {
      id: user.id,
      name: user.name,
      phone: user.phone,
      email: user.email,
      level: user.studentProfile?.level,
      branch: user.studentProfile?.branch,
      emergencyContact: user.studentProfile?.emergencyContact,
      status: user.studentProfile?.status,
      currentGroup: user.studentProfile?.currentGroupReference,
      recentPayments: user.studentProfile?.payments,
      recentAttendance: user.studentProfile?.attendances,
      recentAssessments: user.studentProfile?.assessments,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }

    return NextResponse.json({
      student: studentData,
      syncTimestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error syncing student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
