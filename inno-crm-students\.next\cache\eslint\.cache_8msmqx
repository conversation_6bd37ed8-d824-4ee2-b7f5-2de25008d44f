[{"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\dashboard-card.tsx": "1", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\auth.ts": "2", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\database.ts": "3", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\utils.ts": "4", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\ui\\dashboard-card.tsx": "8", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\auth.ts": "9", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\database.ts": "10", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\utils.ts": "11", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\types\\next-auth.d.ts": "12", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "13", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\students\\route.ts": "14", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\students\\[id]\\route.ts": "15", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\error\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\signin\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\layout.tsx": "18", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\payments\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\profile\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\layout\\student-sidebar.tsx": "21", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\providers\\SessionProvider.tsx": "22", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\api-clients\\staff.ts": "23", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\services\\sync.ts": "24"}, {"size": 1037, "mtime": 1750433999424, "results": "25", "hashOfConfig": "26"}, {"size": 1718, "mtime": 1750437968855, "results": "27", "hashOfConfig": "26"}, {"size": 271, "mtime": 1750433874619, "results": "28", "hashOfConfig": "26"}, {"size": 166, "mtime": 1750433890252, "results": "29", "hashOfConfig": "26"}, {"size": 6085, "mtime": 1750439332771, "results": "30", "hashOfConfig": "26"}, {"size": 845, "mtime": 1750438731281, "results": "31", "hashOfConfig": "26"}, {"size": 314, "mtime": 1750439030003, "results": "32", "hashOfConfig": "26"}, {"size": 1037, "mtime": 1750435154954, "results": "33", "hashOfConfig": "26"}, {"size": 2116, "mtime": 1750441057519, "results": "34", "hashOfConfig": "26"}, {"size": 296, "mtime": 1750440969335, "results": "35", "hashOfConfig": "26"}, {"size": 166, "mtime": 1750435179070, "results": "36", "hashOfConfig": "26"}, {"size": 1056, "mtime": 1750438661347, "results": "37", "hashOfConfig": "26"}, {"size": 157, "mtime": 1750438672167, "results": "38", "hashOfConfig": "26"}, {"size": 5015, "mtime": 1750441009037, "results": "39", "hashOfConfig": "26"}, {"size": 7825, "mtime": 1750440993287, "results": "40", "hashOfConfig": "26"}, {"size": 1723, "mtime": 1750438699276, "results": "41", "hashOfConfig": "26"}, {"size": 4201, "mtime": 1750441522385, "results": "42", "hashOfConfig": "26"}, {"size": 1144, "mtime": 1750439047415, "results": "43", "hashOfConfig": "26"}, {"size": 7772, "mtime": 1750439392880, "results": "44", "hashOfConfig": "26"}, {"size": 8242, "mtime": 1750441177552, "results": "45", "hashOfConfig": "26"}, {"size": 3273, "mtime": 1750439062491, "results": "46", "hashOfConfig": "26"}, {"size": 321, "mtime": 1750438742852, "results": "47", "hashOfConfig": "26"}, {"size": 3749, "mtime": 1750441094530, "results": "48", "hashOfConfig": "26"}, {"size": 10245, "mtime": 1750441133662, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19o6u7n", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\dashboard-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\ui\\dashboard-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\types\\next-auth.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\students\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\api\\inter-server\\students\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\error\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\auth\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\payments\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\layout\\student-sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\providers\\SessionProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\api-clients\\staff.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\services\\sync.ts", [], []]