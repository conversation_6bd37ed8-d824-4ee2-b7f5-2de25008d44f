import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/database'
import bcrypt from 'bcryptjs'
import { z } from 'zod'

// Middleware to verify inter-server requests
function verifyInterServerRequest(request: NextRequest) {
  const apiKey = request.headers.get('X-API-Key')
  const serverSource = request.headers.get('X-Server-Source')
  
  if (!apiKey || apiKey !== process.env.STAFF_API_KEY) {
    return false
  }
  
  if (serverSource !== 'staff') {
    return false
  }
  
  return true
}

const createStudentSchema = z.object({
  id: z.string(),
  name: z.string().min(1),
  phone: z.string().min(1),
  email: z.string().email().optional(),
  password: z.string().min(6),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']),
  branch: z.string().min(1),
  emergencyContact: z.string().optional(),
})

// POST /api/inter-server/students - Create student from staff service
export async function POST(request: NextRequest) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await request.json()
    const validatedData = createStudentSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { phone: validatedData.phone }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this phone number already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create user and student profile
    const user = await prisma.user.create({
      data: {
        id: validatedData.id,
        phone: validatedData.phone,
        email: validatedData.email,
        name: validatedData.name,
        role: 'STUDENT',
        password: hashedPassword,
        studentProfile: {
          create: {
            id: `profile_${validatedData.id}`,
            level: validatedData.level,
            branch: validatedData.branch,
            emergencyContact: validatedData.emergencyContact,
            status: 'ACTIVE',
          }
        }
      },
      include: {
        studentProfile: true,
      }
    })

    return NextResponse.json({
      id: user.id,
      name: user.name,
      phone: user.phone,
      email: user.email,
      level: user.studentProfile?.level,
      branch: user.studentProfile?.branch,
      emergencyContact: user.studentProfile?.emergencyContact,
      status: user.studentProfile?.status,
      createdAt: user.createdAt,
    }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    
    console.error('Error creating student:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// GET /api/inter-server/students - Get students for sync
export async function GET(request: NextRequest) {
  if (!verifyInterServerRequest(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const { searchParams } = new URL(request.url)
    const lastSync = searchParams.get('lastSync')
    const limit = parseInt(searchParams.get('limit') || '100')

    const where: any = {
      role: 'STUDENT'
    }
    
    if (lastSync) {
      where.updatedAt = {
        gte: new Date(lastSync)
      }
    }

    const students = await prisma.user.findMany({
      where,
      include: {
        studentProfile: {
          include: {
            currentGroupReference: {
              include: {
                teacherReference: true,
              }
            },
            payments: {
              orderBy: { createdAt: 'desc' },
              take: 5,
            },
            attendances: {
              orderBy: { createdAt: 'desc' },
              take: 10,
            }
          }
        }
      },
      orderBy: { updatedAt: 'desc' },
      take: limit,
    })

    const transformedStudents = students.map(user => ({
      id: user.id,
      name: user.name,
      phone: user.phone,
      email: user.email,
      level: user.studentProfile?.level,
      branch: user.studentProfile?.branch,
      emergencyContact: user.studentProfile?.emergencyContact,
      status: user.studentProfile?.status,
      currentGroup: user.studentProfile?.currentGroupReference,
      recentPayments: user.studentProfile?.payments,
      recentAttendance: user.studentProfile?.attendances,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }))

    return NextResponse.json({
      students: transformedStudents,
      syncTimestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error fetching students for sync:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
